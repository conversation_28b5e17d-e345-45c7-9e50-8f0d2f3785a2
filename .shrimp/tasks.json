{"tasks": [{"id": "de925221-d628-4a0b-998a-677c76a158a4", "name": "Snippets目录李继刚文件发现与清单建立", "description": "递归扫描Snippets根目录及其所有子目录，识别文件名包含prompt、李继刚或lijigang的所有文件，建立完整的待处理文档清单。重点检查readwise/Full Document Contents/Tweets和readwise/Tweets目录下的李继刚相关文件，记录文件路径、类型和初步内容特征", "notes": "这是整个项目的基础任务，必须确保不遗漏任何李继刚相关文件。特别关注readwise目录下的推文内容，这些可能包含完整的提示词", "status": "pending", "dependencies": [], "createdAt": "2025-08-04T03:28:56.387Z", "updatedAt": "2025-08-04T03:28:56.387Z", "relatedFiles": [{"path": "Snippets/", "type": "REFERENCE", "description": "主要扫描目录"}, {"path": "Snippets/readwise/", "type": "REFERENCE", "description": "重点扫描的子目录"}, {"path": "Snippets/Prompt三则(6).md", "type": "TO_MODIFY", "description": "已知的多提示词文档"}], "implementationGuide": "使用view工具递归扫描Snippets目录结构，按以下步骤执行：\\n1. 扫描Snippets根目录，识别直接包含关键词的文件\\n2. 深入readwise子目录，重点检查Tweets和Full Document Contents\\n3. 对每个发现的文件记录：文件路径、文件大小、修改时间\\n4. 初步判断文件类型：单一提示词、多提示词、非提示词内容\\n5. 生成结构化清单，按优先级排序（提示词文档优先）", "verificationCriteria": "完成后应生成包含所有李继刚相关文件的结构化清单，清单应包含文件路径、类型判断、优先级排序，确保无遗漏", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}, {"id": "a5234ab9-999e-44b8-9740-32038a064583", "name": "提示词文档内容分析与分类", "description": "对发现的李继刚相关文件进行深度内容分析，识别真正的提示词文档，区分单一提示词与多提示词文档，提取主题关键词用于后续命名和分类。重点分析文档结构特征，如Role定义、===分隔符、系统指令等", "notes": "需要仔细区分真正的提示词文档和普通文章。多提示词文档需要准确标记分割点，确保拆分后每个部分都是完整的提示词", "status": "pending", "dependencies": [{"taskId": "de925221-d628-4a0b-998a-677c76a158a4"}], "createdAt": "2025-08-04T03:28:56.387Z", "updatedAt": "2025-08-04T03:28:56.387Z", "relatedFiles": [{"path": "Snippets/Prompt三则(6).md", "type": "TO_MODIFY", "description": "典型的多提示词文档，包含三个独立提示词"}, {"path": "Documents/李继刚-汉语新解.md", "type": "REFERENCE", "description": "标准化李继刚文档格式参考"}], "implementationGuide": "对清单中的每个文件执行内容分析：\\n1. 读取文件内容，检查是否包含提示词特征标识\\n2. 识别提示词结构：Role、Prompt、系统指令、===分隔符\\n3. 判断文档类型：单一提示词、多提示词、非提示词\\n4. 对多提示词文档，标记分割点和独立提示词数量\\n5. 提取主题关键词，用于生成标准化文件名\\n6. 评估内容质量和完整性，确定处理优先级", "verificationCriteria": "完成后应准确识别所有真正的提示词文档，正确区分单一和多提示词类型，为多提示词文档准确标记分割点", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}, {"id": "a05dfd4b-3f44-453a-a650-0c5f507c8899", "name": "Documents目录现有文档检查与版本比较", "description": "检查Documents目录下现有的李继刚文档，与Snippets中发现的文件进行内容比较，识别重复文档和版本差异。基于内容完整性、YAML规范性、功能描述详细程度等标准，确定保留哪个版本", "notes": "版本比较需要综合考虑内容完整性和格式规范性。优先保留YAML规范完整、内容详细的版本", "status": "pending", "dependencies": [{"taskId": "a5234ab9-999e-44b8-9740-32038a064583"}], "createdAt": "2025-08-04T03:28:56.387Z", "updatedAt": "2025-08-04T03:28:56.387Z", "relatedFiles": [{"path": "Documents/", "type": "REFERENCE", "description": "现有李继刚文档目录"}, {"path": "Templates/文档模板.md", "type": "REFERENCE", "description": "YAML规范标准"}], "implementationGuide": "执行版本管理和去重分析：\\n1. 扫描Documents目录下所有李继刚-*.md文件\\n2. 提取每个文档的核心内容特征和主题关键词\\n3. 与Snippets中的文件进行内容相似度比较\\n4. 评估版本质量：YAML完整性、内容详细程度、格式规范性\\n5. 标记重复文档，确定保留策略（保留最完整版本）\\n6. 生成版本比较报告，列出需要删除的重复文件", "verificationCriteria": "生成详细的版本比较报告，明确标识重复文档和推荐保留的版本，确保去重策略合理", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}, {"id": "06ba89cf-e128-4260-8fb5-2c1fb554a716", "name": "多提示词文档拆分处理", "description": "对识别出的多提示词文档进行拆分处理，将每个独立的提示词提取为单独文件。按照李继刚文档标准生成YAML头部，确保每个拆分后的文件都符合规范要求", "notes": "拆分时要确保每个独立提示词的完整性，YAML头部必须严格按照Templates/文档模板.md规范填写", "status": "pending", "dependencies": [{"taskId": "a5234ab9-999e-44b8-9740-32038a064583"}], "createdAt": "2025-08-04T03:28:56.388Z", "updatedAt": "2025-08-04T03:28:56.388Z", "relatedFiles": [{"path": "Snippets/Prompt三则(6).md", "type": "TO_MODIFY", "description": "需要拆分的多提示词文档", "lineStart": 18, "lineEnd": 42}, {"path": "Templates/文档模板.md", "type": "REFERENCE", "description": "YAML模板标准"}, {"path": "Documents/李继刚-汉语新解.md", "type": "REFERENCE", "description": "标准格式参考"}], "implementationGuide": "执行多提示词文档的拆分操作：\\n1. 读取多提示词文档内容，按分割点进行拆分\\n2. 为每个独立提示词生成标准化YAML头部\\n3. 应用李继刚文档标准：t/clipping标签、[[李继刚]][[Prompt]]双链、m/攻略标记\\n4. 根据提示词主题生成标准化文件名：李继刚-主题.md\\n5. 填写描述字段，详细说明提示词功能和用途\\n6. 设置创建日期为2025-07-30，保持与现有文档一致", "verificationCriteria": "拆分后的每个文件都应该是完整的独立提示词，YAML头部完全符合李继刚文档标准，文件名规范", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}, {"id": "e12b2068-ff6c-46b5-afc1-17ca41b54687", "name": "单一提示词文档标准化处理", "description": "对识别出的单一提示词文档进行标准化处理，应用李继刚文档YAML规范，确保所有字段完整且格式正确。重点处理readwise目录下的推文提示词文档", "notes": "特别注意readwise目录下的推文文档，这些通常包含完整的提示词内容，需要仔细提取和标准化", "status": "pending", "dependencies": [{"taskId": "a5234ab9-999e-44b8-9740-32038a064583"}], "createdAt": "2025-08-04T03:28:56.388Z", "updatedAt": "2025-08-04T03:28:56.388Z", "relatedFiles": [{"path": "Snippets/readwise/Full Document Contents/Tweets/", "type": "TO_MODIFY", "description": "包含多个李继刚推文提示词"}, {"path": "Templates/文档模板.md", "type": "REFERENCE", "description": "YAML标准模板"}], "implementationGuide": "对单一提示词文档执行标准化：\\n1. 读取原始文档内容，保留核心提示词部分\\n2. 生成标准化YAML头部，包含所有必需字段\\n3. 确保包含李继刚标准标签：t/resource, c/资料/文档, t/doc, t/clipping, m/攻略\\n4. 添加必需双链：[[李继刚]], [[Prompt]]\\n5. 根据提示词功能添加相关主题双链\\n6. 填写详细的描述字段，说明提示词用途和特点\\n7. 生成规范的文件名：李继刚-主题.md", "verificationCriteria": "所有单一提示词文档都应该完全符合李继刚文档标准，YAML头部完整，双链关系正确", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}, {"id": "1538ac81-aab3-4c9f-bab0-ae73464bfd58", "name": "标准化文档迁移到Documents目录", "description": "将处理完成的标准化李继刚文档迁移到Documents目录，执行原子化操作确保数据零丢失。先创建新文件并验证完整性，再删除源文件", "notes": "严格遵循原子化操作原则，确保在任何情况下都不会丢失数据。每个操作都要先创建后删除", "status": "pending", "dependencies": [{"taskId": "06ba89cf-e128-4260-8fb5-2c1fb554a716"}, {"taskId": "e12b2068-ff6c-46b5-afc1-17ca41b54687"}, {"taskId": "a05dfd4b-3f44-453a-a650-0c5f507c8899"}], "createdAt": "2025-08-04T03:28:56.388Z", "updatedAt": "2025-08-04T03:28:56.388Z", "relatedFiles": [{"path": "Documents/", "type": "CREATE", "description": "目标迁移目录"}, {"path": "Snippets/", "type": "TO_MODIFY", "description": "源文件目录，处理后删除"}], "implementationGuide": "执行原子化文档迁移：\\n1. 在Documents目录创建标准化的李继刚文档\\n2. 验证新文件的内容完整性和YAML格式正确性\\n3. 检查文件名是否符合李继刚-主题.md格式\\n4. 确认所有必需字段都已正确填写\\n5. 验证双链关系和标签的准确性\\n6. 只有在新文件完全正确后，才删除源文件\\n7. 记录每个文件的迁移状态和结果", "verificationCriteria": "所有李继刚提示词文档都应该成功迁移到Documents目录，源文件正确删除，无数据丢失", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}, {"id": "086638ba-9f2e-4340-9771-6710e32712ab", "name": "重复文档清理与版本整合", "description": "根据版本比较结果，清理重复的李继刚文档，保留最优版本。确保Documents目录下没有重复内容，所有保留的文档都是最完整和最规范的版本", "notes": "清理时要特别小心，确保不删除有价值的独特内容。如有疑问，优先保留内容更完整的版本", "status": "pending", "dependencies": [{"taskId": "1538ac81-aab3-4c9f-bab0-ae73464bfd58"}], "createdAt": "2025-08-04T03:28:56.388Z", "updatedAt": "2025-08-04T03:28:56.388Z", "relatedFiles": [{"path": "Documents/", "type": "TO_MODIFY", "description": "需要清理重复文档的目录"}], "implementationGuide": "执行重复文档清理：\\n1. 根据版本比较报告，识别需要删除的重复文档\\n2. 确认保留版本的质量和完整性\\n3. 对于内容有差异的重复文档，合并有价值的信息\\n4. 删除低质量或不完整的重复版本\\n5. 更新保留文档的YAML字段，确保信息完整\\n6. 验证清理后的文档集合，确保无重复且内容完整", "verificationCriteria": "Documents目录下应该没有重复的李继刚文档，所有保留的文档都是最优版本，内容完整且格式规范", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}, {"id": "fa280e75-c95c-41dd-912c-4b2c621ff2b0", "name": "整理结果验证与质量检查", "description": "对整个李继刚文档整理项目进行全面的质量检查和验证，确保所有文档都符合标准，没有遗漏或错误。生成整理报告总结项目成果", "notes": "这是项目的最终验证步骤，必须确保整理质量达到Vision的完美主义标准", "status": "pending", "dependencies": [{"taskId": "086638ba-9f2e-4340-9771-6710e32712ab"}], "createdAt": "2025-08-04T03:28:56.388Z", "updatedAt": "2025-08-04T03:28:56.388Z", "relatedFiles": [{"path": "Documents/", "type": "REFERENCE", "description": "最终整理结果目录"}, {"path": "Templates/文档模板.md", "type": "REFERENCE", "description": "验证标准"}], "implementationGuide": "执行全面的质量验证：\\n1. 检查Documents目录下所有李继刚文档的YAML规范性\\n2. 验证文件命名是否符合李继刚-主题.md格式\\n3. 确认所有必需标签和双链都已正确添加\\n4. 检查描述字段的完整性和准确性\\n5. 验证没有遗漏的Snippets中的李继刚文档\\n6. 生成整理报告，统计处理的文档数量和类型\\n7. 记录发现的问题和解决方案", "verificationCriteria": "所有李继刚文档都应该完全符合标准规范，生成详细的整理报告，确认项目目标100%达成", "analysisResult": "系统性处理Snippets目录下的李继刚相关文件整理任务，通过提示词识别迁移、多提示词文档拆分、重复内容检查与版本管理，确保所有文件符合李继刚文档标准规范，实现数据零丢失的原子化操作"}]}